E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\BinanceOrderBookWPF.csproj.AssemblyReference.cache
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\Styles\TradingTheme.baml
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\MainWindow.g.cs
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\Views\AdvancedControlPanel.g.cs
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\Views\OrderBookDepthView.g.cs
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\Views\PriceInfoView.g.cs
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\Views\TradeChartView.g.cs
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\Views\TradeTickerView.g.cs
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\App.g.cs
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\GeneratedInternalTypeHelper.g.cs
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\BinanceOrderBookWPF_MarkupCompile.cache
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\BinanceOrderBookWPF_MarkupCompile.lref
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\BinanceOrderBookWPF.exe
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\BinanceOrderBookWPF.deps.json
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\BinanceOrderBookWPF.runtimeconfig.json
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\BinanceOrderBookWPF.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\BinanceOrderBookWPF.pdb
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\HarfBuzzSharp.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\Microsoft.Extensions.Configuration.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\Microsoft.Extensions.Configuration.Binder.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\Microsoft.Extensions.DependencyInjection.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\Microsoft.Extensions.DependencyInjection.Abstractions.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\Microsoft.Extensions.Logging.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\Microsoft.Extensions.Logging.Abstractions.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\Microsoft.Extensions.Logging.Configuration.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\Microsoft.Extensions.Logging.Console.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\Microsoft.Extensions.Options.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\Microsoft.Extensions.Options.ConfigurationExtensions.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\Microsoft.Extensions.Primitives.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\Newtonsoft.Json.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\OpenTK.Compute.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\OpenTK.Core.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\GLWpfControl.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\OpenTK.Graphics.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\OpenTK.Input.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\OpenTK.Mathematics.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\OpenTK.OpenAL.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\OpenTK.Windowing.Common.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\OpenTK.Windowing.Desktop.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\OpenTK.Windowing.GraphicsLibraryFramework.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\ScottPlot.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\ScottPlot.WPF.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\SkiaSharp.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\SkiaSharp.HarfBuzz.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\SkiaSharp.Views.Desktop.Common.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\SkiaSharp.Views.WPF.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\linux-arm\native\libHarfBuzzSharp.so
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\linux-arm64\native\libHarfBuzzSharp.so
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\linux-musl-x64\native\libHarfBuzzSharp.so
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\linux-x64\native\libHarfBuzzSharp.so
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\osx\native\libHarfBuzzSharp.dylib
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\win-arm64\native\libHarfBuzzSharp.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\win-x64\native\libHarfBuzzSharp.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\win-x86\native\libHarfBuzzSharp.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\linux-x64\native\libglfw.so.3.3
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\osx-x64\native\libglfw.3.dylib
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\win-x64\native\glfw3.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\win-x86\native\glfw3.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\linux-arm\native\libSkiaSharp.so
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\linux-arm64\native\libSkiaSharp.so
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\linux-musl-x64\native\libSkiaSharp.so
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\linux-x64\native\libSkiaSharp.so
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\osx\native\libSkiaSharp.dylib
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\win-arm64\native\libSkiaSharp.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\win-x64\native\libSkiaSharp.dll
E:\BinanceOrderBookWPF\bin\Debug\net9.0-windows\runtimes\win-x86\native\libSkiaSharp.dll
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\App.baml
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\MainWindow.baml
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\Views\AdvancedControlPanel.baml
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\Views\OrderBookDepthView.baml
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\Views\PriceInfoView.baml
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\Views\TradeChartView.baml
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\Views\TradeTickerView.baml
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\BinanceOrderBookWPF.g.resources
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\BinanceOrderBookWPF.GeneratedMSBuildEditorConfig.editorconfig
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\BinanceOrderBookWPF.AssemblyInfoInputs.cache
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\BinanceOrderBookWPF.AssemblyInfo.cs
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\BinanceOrderBookWPF.csproj.CoreCompileInputs.cache
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\BinanceO.C101A635.Up2Date
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\BinanceOrderBookWPF.dll
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\refint\BinanceOrderBookWPF.dll
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\BinanceOrderBookWPF.pdb
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\BinanceOrderBookWPF.genruntimeconfig.cache
E:\BinanceOrderBookWPF\obj\Debug\net9.0-windows\ref\BinanceOrderBookWPF.dll
